<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Bottom Controls Overlay -->
    <LinearLayout
        android:id="@+id/bottomControlsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gradient_bottom_overlay"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Progress Bar -->
        <SeekBar
            android:id="@id/exo_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:progressTint="@color/primary_color"
            android:thumbTint="@color/primary_color" />

        <!-- Control Buttons Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Current Time -->
            <TextView
                android:id="@id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <!-- Previous Button -->
            <ImageButton
                android:id="@id/exo_prev"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/previous"
                android:padding="12dp"
                android:src="@drawable/ic_skip_previous"
                android:tint="@color/white" />

            <!-- Rewind Button -->
            <ImageButton
                android:id="@id/exo_rew"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/rewind"
                android:padding="12dp"
                android:src="@drawable/ic_fast_rewind"
                android:tint="@color/white" />

            <!-- Play/Pause Button -->
            <ImageButton
                android:id="@id/exo_play"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginHorizontal="16dp"
                android:background="@drawable/circle_background_primary"
                android:contentDescription="@string/play_video"
                android:padding="16dp"
                android:src="@drawable/ic_play_arrow"
                android:tint="@color/white" />

            <!-- Fast Forward Button -->
            <ImageButton
                android:id="@id/exo_ffwd"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/fast_forward"
                android:padding="12dp"
                android:src="@drawable/ic_fast_forward"
                android:tint="@color/white" />

            <!-- Next Button -->
            <ImageButton
                android:id="@id/exo_next"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/next"
                android:padding="12dp"
                android:src="@drawable/ic_skip_next"
                android:tint="@color/white" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Duration -->
            <TextView
                android:id="@id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <!-- Fullscreen Button -->
            <ImageButton
                android:id="@+id/fullscreenButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/fullscreen"
                android:padding="12dp"
                android:src="@drawable/ic_fullscreen"
                android:tint="@color/white" />

        </LinearLayout>

    </LinearLayout>

    <!-- Center Play Button (when paused) -->
    <ImageButton
        android:id="@+id/centerPlayButton"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/circle_background_translucent"
        android:contentDescription="@string/play_video"
        android:padding="20dp"
        android:src="@drawable/ic_play_arrow"
        android:tint="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
