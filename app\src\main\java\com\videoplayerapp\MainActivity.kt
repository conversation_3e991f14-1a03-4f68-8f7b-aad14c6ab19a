package com.videoplayerapp

import android.Manifest
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.animation.AnimationUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.button.MaterialButton
import com.karumi.dexter.Dexter
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionDeniedResponse
import com.karumi.dexter.listener.PermissionGrantedResponse
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.single.PermissionListener
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import com.karumi.dexter.listener.multi.MultiplePermissionsReport
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var videosRecyclerView: RecyclerView
    private lateinit var loadingAnimation: LottieAnimationView
    private lateinit var noVideosLayout: View
    private lateinit var permissionLayout: View
    private lateinit var grantPermissionButton: MaterialButton
    
    private lateinit var videoAdapter: VideoAdapter
    private val videos = mutableListOf<Video>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        setupRecyclerView()
        checkPermissions()
    }

    private fun initViews() {
        videosRecyclerView = findViewById(R.id.videosRecyclerView)
        loadingAnimation = findViewById(R.id.loadingAnimation)
        noVideosLayout = findViewById(R.id.noVideosLayout)
        permissionLayout = findViewById(R.id.permissionLayout)
        grantPermissionButton = findViewById(R.id.grantPermissionButton)
        
        grantPermissionButton.setOnClickListener {
            checkPermissions()
        }
    }

    private fun setupRecyclerView() {
        videoAdapter = VideoAdapter(this) { video ->
            openVideoPlayer(video)
        }
        
        videosRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = videoAdapter
            
            // Add scroll animation
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                    
                    for (i in firstVisiblePosition..lastVisiblePosition) {
                        val view = layoutManager.findViewByPosition(i)
                        view?.let { animateViewOnScroll(it) }
                    }
                }
            })
        }
    }

    private fun animateViewOnScroll(view: View) {
        val animation = AnimationUtils.loadAnimation(this, R.anim.slide_in_right)
        view.startAnimation(animation)
    }

    private fun checkPermissions() {
        val permissions = mutableListOf<String>()

        // Add permissions based on Android version
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
            // Add photo picker permission for Android 14+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                permissions.add(Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED)
            }
        } else {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

        Dexter.withContext(this)
            .withPermissions(permissions)
            .withListener(object : com.karumi.dexter.listener.multi.MultiplePermissionsListener {
                override fun onPermissionsChecked(report: com.karumi.dexter.listener.multi.MultiplePermissionsReport) {
                    if (report.areAllPermissionsGranted()) {
                        hidePermissionLayout()
                        loadVideos()
                    } else {
                        showPermissionLayout()
                    }
                }

                override fun onPermissionRationaleShouldBeShown(
                    permissions: MutableList<com.karumi.dexter.listener.PermissionRequest>,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            }).check()
    }

    private fun loadVideos() {
        showLoading()
        
        lifecycleScope.launch {
            try {
                val videoList = VideoUtils.getAllVideos(this@MainActivity)
                videos.clear()
                videos.addAll(videoList)
                
                runOnUiThread {
                    hideLoading()
                    if (videos.isEmpty()) {
                        showNoVideosMessage()
                    } else {
                        showVideosList()
                        videoAdapter.submitList(videos.toList())
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    hideLoading()
                    showNoVideosMessage()
                }
            }
        }
    }

    private fun openVideoPlayer(video: Video) {
        val intent = Intent(this, VideoPlayerActivity::class.java)
        intent.putExtra("video", video)
        startActivity(intent)
    }

    private fun showLoading() {
        loadingAnimation.visibility = View.VISIBLE
        videosRecyclerView.visibility = View.GONE
        noVideosLayout.visibility = View.GONE
        permissionLayout.visibility = View.GONE
    }

    private fun hideLoading() {
        loadingAnimation.visibility = View.GONE
    }

    private fun showVideosList() {
        videosRecyclerView.visibility = View.VISIBLE
        noVideosLayout.visibility = View.GONE
        permissionLayout.visibility = View.GONE
        
        // Animate RecyclerView entrance
        val animation = AnimationUtils.loadAnimation(this, R.anim.fade_in)
        videosRecyclerView.startAnimation(animation)
    }

    private fun showNoVideosMessage() {
        videosRecyclerView.visibility = View.GONE
        noVideosLayout.visibility = View.VISIBLE
        permissionLayout.visibility = View.GONE
        
        // Animate no videos layout
        val animation = AnimationUtils.loadAnimation(this, R.anim.fade_in)
        noVideosLayout.startAnimation(animation)
    }

    private fun showPermissionLayout() {
        videosRecyclerView.visibility = View.GONE
        noVideosLayout.visibility = View.GONE
        permissionLayout.visibility = View.VISIBLE
        loadingAnimation.visibility = View.GONE
        
        // Animate permission layout
        val animation = AnimationUtils.loadAnimation(this, R.anim.slide_up)
        permissionLayout.startAnimation(animation)
    }

    private fun hidePermissionLayout() {
        permissionLayout.visibility = View.GONE
    }
}
