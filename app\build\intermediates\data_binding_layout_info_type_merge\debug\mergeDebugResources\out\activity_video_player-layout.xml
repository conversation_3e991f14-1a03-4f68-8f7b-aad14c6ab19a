<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_video_player" modulePackage="com.videoplayerapp" filePath="app\src\main\res\layout\activity_video_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_video_player_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/playerView" view="com.google.android.exoplayer2.ui.PlayerView"><Expressions/><location startLine="10" startOffset="4" endLine="22" endOffset="35"/></Target><Target id="@+id/topControlsLayout" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="73" endOffset="18"/></Target><Target id="@+id/backButton" view="ImageButton"><Expressions/><location startLine="38" startOffset="8" endLine="46" endOffset="41"/></Target><Target id="@+id/videoTitle" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="60" endOffset="49"/></Target><Target id="@+id/moreButton" view="ImageButton"><Expressions/><location startLine="63" startOffset="8" endLine="71" endOffset="41"/></Target><Target id="@+id/loadingIndicator" view="ProgressBar"><Expressions/><location startLine="76" startOffset="4" endLine="85" endOffset="51"/></Target></Targets></Layout>