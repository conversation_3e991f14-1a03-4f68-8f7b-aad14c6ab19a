# دليل الإعداد السريع - Quick Setup Guide

## 🚀 البدء السريع

### 1. متطلبات النظام
- **Android Studio** Flamingo أو أحدث
- **JDK 8** أو أحدث  
- **Android SDK 24+**
- جهاز Android أو محاكي

### 2. خطوات الإعداد

#### أ) فتح المشروع
```bash
1. افتح Android Studio
2. اختر "Open an Existing Project"
3. حدد مجلد المشروع
4. انتظر حتى يتم تحميل المكتبات
```

#### ب) إعداد الجهاز
```bash
1. فعّل "Developer Options" على جهازك
2. فعّل "USB Debugging"
3. وصل الجهاز بالكمبيوتر
```

#### ج) تشغيل التطبيق
```bash
1. اضغط على زر "Run" (▶️)
2. أو استخدم Shift+F10
3. أ<PERSON> من Terminal: ./gradlew installDebug
```

### 3. ح<PERSON> المشاكل الشائعة

#### مشكلة Gradle
```bash
# إذا واجهت مشاكل في Gradle
./gradlew clean
./gradlew build
```

#### مشكلة الصلاحيات
```bash
# تأكد من إضافة الصلاحيات في AndroidManifest.xml
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

#### مشكلة المكتبات
```bash
# في build.gradle (Module: app)
implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
implementation 'com.airbnb.android:lottie:6.1.0'
implementation 'com.github.bumptech.glide:glide:4.16.0'
```

### 4. اختبار التطبيق

#### أ) إضافة فيديوهات للاختبار
```bash
1. انسخ بعض ملفات الفيديو إلى جهازك
2. ضعها في مجلد Downloads أو Movies
3. شغّل التطبيق واختبر التشغيل
```

#### ب) اختبار الميزات
- ✅ عرض قائمة الفيديوهات
- ✅ تشغيل الفيديو
- ✅ عناصر التحكم
- ✅ الوضع الأفقي
- ✅ الانيميشن

### 5. بناء APK للتوزيع

#### Debug APK
```bash
./gradlew assembleDebug
# الملف في: app/build/outputs/apk/debug/
```

#### Release APK
```bash
./gradlew assembleRelease
# الملف في: app/build/outputs/apk/release/
```

### 6. نصائح التطوير

#### أ) تحسين الأداء
- استخدم ProGuard للإصدار النهائي
- قم بضغط الصور والموارد
- اختبر على أجهزة مختلفة

#### ب) التصحيح
- استخدم Logcat لمتابعة الأخطاء
- فعّل Layout Inspector لفحص الواجهة
- استخدم Profiler لمراقبة الأداء

#### ج) الاختبار
- اختبر على إصدارات Android مختلفة
- اختبر مع أحجام شاشة مختلفة
- اختبر مع أنواع فيديو مختلفة

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md
2. راجع رسائل الخطأ في Logcat
3. تأكد من تحديث Android Studio والمكتبات

**نصيحة**: احتفظ بنسخة احتياطية من المشروع قبل إجراء تغييرات كبيرة!
