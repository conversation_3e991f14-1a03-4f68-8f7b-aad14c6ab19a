package com.videoplayerapp

import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.util.Util

class VideoPlayerActivity : AppCompatActivity() {

    private lateinit var playerView: PlayerView
    private lateinit var backButton: ImageButton
    private lateinit var videoTitle: TextView
    
    private var exoPlayer: ExoPlayer? = null
    private var video: Video? = null
    private var playWhenReady = true
    private var currentWindow = 0
    private var playbackPosition = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_player)
        
        // Get video from intent
        video = intent.getParcelableExtra("video")
        
        initViews()
        setupFullScreen()
        setupPlayer()
    }

    private fun initViews() {
        playerView = findViewById(R.id.playerView)
        backButton = findViewById(R.id.backButton)
        videoTitle = findViewById(R.id.videoTitle)
        
        // Set video title
        video?.let {
            videoTitle.text = it.getFileName()
        }
        
        // Back button click listener
        backButton.setOnClickListener {
            finish()
        }
        
        // Hide controls initially
        hideSystemUI()
    }

    private fun setupFullScreen() {
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        // Hide status bar and navigation bar
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        supportActionBar?.hide()
    }

    private fun setupPlayer() {
        video?.let { videoData ->
            exoPlayer = ExoPlayer.Builder(this).build()
            
            playerView.player = exoPlayer
            
            val mediaItem = MediaItem.fromUri(Uri.parse(videoData.path))
            exoPlayer?.setMediaItem(mediaItem)
            
            exoPlayer?.playWhenReady = playWhenReady
            exoPlayer?.seekTo(currentWindow, playbackPosition)
            exoPlayer?.prepare()
            
            // Add player listener
            exoPlayer?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(state: Int) {
                    when (state) {
                        Player.STATE_BUFFERING -> {
                            // Show loading indicator if needed
                        }
                        Player.STATE_READY -> {
                            // Hide loading indicator
                        }
                        Player.STATE_ENDED -> {
                            // Video ended, you can add replay functionality here
                        }
                    }
                }
            })
            
            // Setup player view click listener to toggle controls
            playerView.setOnClickListener {
                toggleControlsVisibility()
            }
        }
    }

    private fun toggleControlsVisibility() {
        if (playerView.isControllerVisible) {
            hideControls()
        } else {
            showControls()
        }
    }

    private fun showControls() {
        playerView.showController()
        backButton.visibility = View.VISIBLE
        videoTitle.visibility = View.VISIBLE
        showSystemUI()
    }

    private fun hideControls() {
        playerView.hideController()
        backButton.visibility = View.GONE
        videoTitle.visibility = View.GONE
        hideSystemUI()
    }

    private fun hideSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, playerView).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }

    private fun showSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, true)
        WindowInsetsControllerCompat(window, playerView).show(WindowInsetsCompat.Type.systemBars())
    }

    override fun onStart() {
        super.onStart()
        if (Util.SDK_INT >= 24) {
            initializePlayer()
        }
    }

    override fun onResume() {
        super.onResume()
        if (Util.SDK_INT < 24 || exoPlayer == null) {
            initializePlayer()
        }
    }

    override fun onPause() {
        super.onPause()
        if (Util.SDK_INT < 24) {
            releasePlayer()
        }
    }

    override fun onStop() {
        super.onStop()
        if (Util.SDK_INT >= 24) {
            releasePlayer()
        }
    }

    private fun initializePlayer() {
        if (exoPlayer == null) {
            setupPlayer()
        }
    }

    private fun releasePlayer() {
        exoPlayer?.let { player ->
            playbackPosition = player.currentPosition
            currentWindow = player.currentWindowIndex
            playWhenReady = player.playWhenReady
            player.release()
            exoPlayer = null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }
}
