{"logs": [{"outputFile": "com.videoplayerapp-mergeDebugResources-36:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5d9e96f88c57d15875888b62481fb57\\transformed\\exoplayer-ui-2.19.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3529,3595,3648,3710,3786,3862", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3524,3590,3643,3705,3781,3857,3915"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,594,5270,5356,5444,5523,5621,5716,5793,5860,5960,6060,6126,6195,6262,6333,6464,6583,6709,6780,6866,6942,7019,7122,7227,7291,8053,8106,8164,8212,8273,8338,8408,8474,8546,8616,8684,8750,8815,8881,8934,8996,9072,9148", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "381,589,772,5351,5439,5518,5616,5711,5788,5855,5955,6055,6121,6190,6257,6328,6459,6578,6704,6775,6861,6937,7014,7117,7222,7286,7350,8101,8159,8207,8268,8333,8403,8469,8541,8611,8679,8745,8810,8876,8929,8991,9067,9143,9201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95c2f2dde71652be877f11a0d58ef6e2\\transformed\\material-1.10.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2968,3058,3139,3195,3251,3317,3396,3477,3565,3653,3732,3809,3891,3980,4081,4165,4257,4350,4451,4525,4617,4719,4771,4855,4921,5013,5101,5163,5227,5290,5360,5471,5576,5682,5781,5841,5901", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2963,3053,3134,3190,3246,3312,3391,3472,3560,3648,3727,3804,3886,3975,4076,4160,4252,4345,4446,4520,4612,4714,4766,4850,4916,5008,5096,5158,5222,5285,5355,5466,5571,5677,5776,5836,5896,5981"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "777,3702,3783,3863,3948,4050,4874,4979,5112,5192,9206,9302,9381,9444,9539,9603,9672,9735,9809,9873,9929,10050,10108,10170,10226,10303,10442,10530,10610,10750,10830,10910,11059,11149,11230,11286,11342,11408,11487,11568,11656,11744,11823,11900,11982,12071,12172,12256,12348,12441,12542,12616,12708,12810,12862,12946,13012,13104,13192,13254,13318,13381,13451,13562,13667,13773,13872,13932,13992", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "947,3778,3858,3943,4045,4141,4974,5107,5187,5265,9297,9376,9439,9534,9598,9667,9730,9804,9868,9924,10045,10103,10165,10221,10298,10437,10525,10605,10745,10825,10905,11054,11144,11225,11281,11337,11403,11482,11563,11651,11739,11818,11895,11977,12066,12167,12251,12343,12436,12537,12611,12703,12805,12857,12941,13007,13099,13187,13249,13313,13376,13446,13557,13662,13768,13867,13927,13987,14072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4f83af3dd64ab85dfcef8f00ae0b8bf9\\transformed\\exoplayer-core-2.19.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7355,7429,7492,7557,7636,7713,7789,7888,7984", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "7424,7487,7552,7631,7708,7784,7883,7979,8048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f77e990dfb2eeb1d5f51a8568b08c12f\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "952,1056,1160,1268,1353,1454,1582,1668,1749,1841,1935,2032,2126,2226,2320,2416,2511,2603,2695,2776,2884,2991,3098,3207,3312,3426,3603,14077", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "1051,1155,1263,1348,1449,1577,1663,1744,1836,1930,2027,2121,2221,2315,2411,2506,2598,2690,2771,2879,2986,3093,3202,3307,3421,3598,3697,14155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b85d503f681dad467ed38263c2abf31\\transformed\\core-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4146,4245,4347,4447,4545,4652,4758,14160", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "4240,4342,4442,4540,4647,4753,4869,14256"}}]}]}