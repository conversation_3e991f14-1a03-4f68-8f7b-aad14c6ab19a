{"logs": [{"outputFile": "com.videoplayerapp-mergeDebugResources-36:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f77e990dfb2eeb1d5f51a8568b08c12f\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,953,1047,1148,1230,1328,1434,1514,1589,1680,1773,1868,1962,2062,2155,2250,2344,2435,2526,2606,2704,2798,2893,2993,3090,3190,3342,12289", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "948,1042,1143,1225,1323,1429,1509,1584,1675,1768,1863,1957,2057,2150,2245,2339,2430,2521,2601,2699,2793,2888,2988,3085,3185,3337,3431,12363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95c2f2dde71652be877f11a0d58ef6e2\\transformed\\material-1.10.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2494,2573,2648,2698,2749,2815,2879,2949,3026,3097,3165,3236,3303,3373,3466,3546,3620,3700,3782,3854,3919,3991,4039,4112,4176,4251,4328,4390,4454,4517,4584,4668,4746,4826,4904,4958,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2489,2568,2643,2693,2744,2810,2874,2944,3021,3092,3160,3231,3298,3368,3461,3541,3615,3695,3777,3849,3914,3986,4034,4107,4171,4246,4323,4385,4449,4512,4579,4663,4741,4821,4899,4953,5008,5080"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "707,3436,3501,3565,3634,3708,4464,4547,4653,4728,8130,8211,8273,8330,8417,8477,8535,8593,8652,8709,8763,8858,8914,8971,9025,9091,9195,9270,9347,9468,9533,9598,9698,9777,9852,9902,9953,10019,10083,10153,10230,10301,10369,10440,10507,10577,10670,10750,10824,10904,10986,11058,11123,11195,11243,11316,11380,11455,11532,11594,11658,11721,11788,11872,11950,12030,12108,12162,12217", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "851,3496,3560,3629,3703,3782,4542,4648,4723,4785,8206,8268,8325,8412,8472,8530,8588,8647,8704,8758,8853,8909,8966,9020,9086,9190,9265,9342,9463,9528,9593,9693,9772,9847,9897,9948,10014,10078,10148,10225,10296,10364,10435,10502,10572,10665,10745,10819,10899,10981,11053,11118,11190,11238,11311,11375,11450,11527,11589,11653,11716,11783,11867,11945,12025,12103,12157,12212,12284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5d9e96f88c57d15875888b62481fb57\\transformed\\exoplayer-ui-2.19.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1599,1687,1776,1840,1909,1974,2032,2109,2185,2246,2311,2364,2421,2467,2526,2582,2644,2701,2761,2817,2873,2937,3000,3064,3114,3170,3240,3310", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1594,1682,1771,1835,1904,1969,2027,2104,2180,2241,2306,2359,2416,2462,2521,2577,2639,2696,2756,2812,2868,2932,2995,3059,3109,3165,3235,3305,3358"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,549,4790,4864,4937,5008,5089,5167,5226,5287,5364,5440,5504,5565,5624,5689,5777,5865,5954,6018,6087,6152,6210,6287,6363,6424,7078,7131,7188,7234,7293,7349,7411,7468,7528,7584,7640,7704,7767,7831,7881,7937,8007,8077", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "377,544,702,4859,4932,5003,5084,5162,5221,5282,5359,5435,5499,5560,5619,5684,5772,5860,5949,6013,6082,6147,6205,6282,6358,6419,6484,7126,7183,7229,7288,7344,7406,7463,7523,7579,7635,7699,7762,7826,7876,7932,8002,8072,8125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b85d503f681dad467ed38263c2abf31\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3787,3879,3979,4073,4170,4266,4364,12368", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3874,3974,4068,4165,4261,4359,4459,12464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4f83af3dd64ab85dfcef8f00ae0b8bf9\\transformed\\exoplayer-core-2.19.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6489,6548,6606,6666,6722,6794,6853,6935,7015", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "6543,6601,6661,6717,6789,6848,6930,7010,7073"}}]}]}