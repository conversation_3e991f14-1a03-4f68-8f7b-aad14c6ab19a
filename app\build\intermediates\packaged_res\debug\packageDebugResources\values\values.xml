<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#FFFF4081</color>
    <color name="background_color">#FF121212</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#FF2C2C2C</color>
    <color name="overlay_background">#80000000</color>
    <color name="primary_color">#FF1976D2</color>
    <color name="primary_dark">#FF0D47A1</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="surface_color">#FF1E1E1E</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#FFFFFFFF</color>
    <color name="text_secondary">#FFBDBDBD</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">مشغل الفيديو</string>
    <string name="back">رجوع</string>
    <string name="fast_forward">تقديم سريع</string>
    <string name="fullscreen">ملء الشاشة</string>
    <string name="grant_permission">منح الصلاحية</string>
    <string name="loading">جاري التحميل...</string>
    <string name="more_options">خيارات أكثر</string>
    <string name="next">التالي</string>
    <string name="no_videos_found">لم يتم العثور على فيديوهات</string>
    <string name="pause_video">إيقاف الفيديو</string>
    <string name="permission_message">نحتاج إلى صلاحية الوصول للملفات لعرض الفيديوهات</string>
    <string name="permission_required">الصلاحية مطلوبة</string>
    <string name="play_video">تشغيل الفيديو</string>
    <string name="previous">السابق</string>
    <string name="rewind">ترجيع</string>
    <string name="video_duration">مدة الفيديو: %s</string>
    <string name="video_player">مشغل الفيديو</string>
    <style name="CardViewStyle">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="Theme.VideoPlayerApp" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:navigationBarColor">@color/background_color</item>
    </style>
    <style name="Theme.VideoPlayerApp.FullScreen" parent="Theme.VideoPlayerApp.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="Theme.VideoPlayerApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>