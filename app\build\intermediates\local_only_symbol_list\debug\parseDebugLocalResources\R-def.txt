R_DEF: Internal format may change without notice
local
anim fade_in
anim slide_in_right
anim slide_up
color accent_color
color background_color
color black
color card_background
color overlay_background
color primary_color
color primary_dark
color purple_200
color purple_500
color purple_700
color surface_color
color teal_200
color teal_700
color text_primary
color text_secondary
color white
drawable circle_background
drawable circle_background_primary
drawable circle_background_translucent
drawable gradient_bottom_overlay
drawable gradient_top_overlay
drawable ic_arrow_back
drawable ic_fast_forward
drawable ic_fast_rewind
drawable ic_fullscreen
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_more_vert
drawable ic_pause
drawable ic_permission
drawable ic_play_arrow
drawable ic_skip_next
drawable ic_skip_previous
drawable ic_video_library
id backButton
id bottomControlsLayout
id cardView
id centerPlayButton
id fullscreenButton
id grantPermissionButton
id loadingAnimation
id loadingIndicator
id moreButton
id noVideosLayout
id permissionLayout
id playButton
id playerView
id toolbar
id topControlsLayout
id videoDuration
id videoResolution
id videoSize
id videoThumbnail
id videoTitle
id videosRecyclerView
layout activity_main
layout activity_video_player
layout custom_player_controls
layout item_video
mipmap ic_launcher
mipmap ic_launcher_round
raw loading_animation
string app_name
string back
string fast_forward
string fullscreen
string grant_permission
string loading
string more_options
string next
string no_videos_found
string pause_video
string permission_message
string permission_required
string play_video
string previous
string rewind
string video_duration
string video_player
style CardViewStyle
style Theme.VideoPlayerApp
style Theme.VideoPlayerApp.FullScreen
style Theme.VideoPlayerApp.NoActionBar
xml backup_rules
xml data_extraction_rules
