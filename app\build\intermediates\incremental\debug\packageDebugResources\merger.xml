<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\src\main\res"><file name="fade_in" path="D:\ccc\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="D:\ccc\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_up" path="D:\ccc\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="circle_background" path="D:\ccc\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_primary" path="D:\ccc\app\src\main\res\drawable\circle_background_primary.xml" qualifiers="" type="drawable"/><file name="circle_background_translucent" path="D:\ccc\app\src\main\res\drawable\circle_background_translucent.xml" qualifiers="" type="drawable"/><file name="gradient_bottom_overlay" path="D:\ccc\app\src\main\res\drawable\gradient_bottom_overlay.xml" qualifiers="" type="drawable"/><file name="gradient_top_overlay" path="D:\ccc\app\src\main\res\drawable\gradient_top_overlay.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="D:\ccc\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_fast_forward" path="D:\ccc\app\src\main\res\drawable\ic_fast_forward.xml" qualifiers="" type="drawable"/><file name="ic_fast_rewind" path="D:\ccc\app\src\main\res\drawable\ic_fast_rewind.xml" qualifiers="" type="drawable"/><file name="ic_fullscreen" path="D:\ccc\app\src\main\res\drawable\ic_fullscreen.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="D:\ccc\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\ccc\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_permission" path="D:\ccc\app\src\main\res\drawable\ic_permission.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="D:\ccc\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_skip_next" path="D:\ccc\app\src\main\res\drawable\ic_skip_next.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous" path="D:\ccc\app\src\main\res\drawable\ic_skip_previous.xml" qualifiers="" type="drawable"/><file name="ic_video_library" path="D:\ccc\app\src\main\res\drawable\ic_video_library.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\ccc\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_video_player" path="D:\ccc\app\src\main\res\layout\activity_video_player.xml" qualifiers="" type="layout"/><file name="custom_player_controls" path="D:\ccc\app\src\main\res\layout\custom_player_controls.xml" qualifiers="" type="layout"/><file name="item_video" path="D:\ccc\app\src\main\res\layout\item_video.xml" qualifiers="" type="layout"/><file name="loading_animation" path="D:\ccc\app\src\main\res\raw\loading_animation.json" qualifiers="" type="raw"/><file path="D:\ccc\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#FF1976D2</color><color name="primary_dark">#FF0D47A1</color><color name="accent_color">#FFFF4081</color><color name="background_color">#FF121212</color><color name="surface_color">#FF1E1E1E</color><color name="card_background">#FF2C2C2C</color><color name="text_primary">#FFFFFFFF</color><color name="text_secondary">#FFBDBDBD</color><color name="overlay_background">#80000000</color></file><file path="D:\ccc\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">مشغل الفيديو</string><string name="video_player">مشغل الفيديو</string><string name="no_videos_found">لم يتم العثور على فيديوهات</string><string name="permission_required">الصلاحية مطلوبة</string><string name="permission_message">نحتاج إلى صلاحية الوصول للملفات لعرض الفيديوهات</string><string name="grant_permission">منح الصلاحية</string><string name="play_video">تشغيل الفيديو</string><string name="pause_video">إيقاف الفيديو</string><string name="video_duration">مدة الفيديو: %s</string><string name="loading">جاري التحميل...</string><string name="back">رجوع</string><string name="more_options">خيارات أكثر</string><string name="previous">السابق</string><string name="next">التالي</string><string name="rewind">ترجيع</string><string name="fast_forward">تقديم سريع</string><string name="fullscreen">ملء الشاشة</string></file><file path="D:\ccc\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.VideoPlayerApp" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:navigationBarColor">@color/background_color</item>
    </style><style name="Theme.VideoPlayerApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.VideoPlayerApp.FullScreen" parent="Theme.VideoPlayerApp.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="CardViewStyle">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
    </style></file><file name="backup_rules" path="D:\ccc\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\ccc\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher_background" path="D:\ccc\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\ccc\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\ccc\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ccc\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ccc\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ccc\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ccc\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ccc\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ccc\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ccc\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\ccc\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\ccc\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ccc\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>