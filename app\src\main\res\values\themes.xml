<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.VideoPlayerApp" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:navigationBarColor">@color/background_color</item>
    </style>

    <style name="Theme.VideoPlayerApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.VideoPlayerApp.FullScreen" parent="Theme.VideoPlayerApp.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <style name="CardViewStyle">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>
</resources>
