[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_fast_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_fast_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_circle_background_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\circle_background_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_skip_next.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_skip_next.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_permission.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_permission.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\xml\\data_extraction_rules.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_pause.xml"}, {"merged": "com.videoplayerapp-debug-38:/drawable_ic_launcher_foreground.xml.flat", "source": "com.videoplayerapp-main-40:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xxhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_skip_previous.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_skip_previous.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\anim\\slide_in_right.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\raw_loading_animation.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\raw\\loading_animation.json"}, {"merged": "com.videoplayerapp-debug-38:/drawable_ic_launcher_background.xml.flat", "source": "com.videoplayerapp-main-40:/drawable/ic_launcher_background.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\layout_item_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\layout\\item_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_gradient_bottom_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\gradient_bottom_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_play_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_play_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\layout\\activity_main.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\layout_custom_player_controls.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\layout\\custom_player_controls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_fast_rewind.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_fast_rewind.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_more_vert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_more_vert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\layout_activity_video_player.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\layout\\activity_video_player.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_video_library.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_video_library.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_ic_fullscreen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\ic_fullscreen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_gradient_top_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\gradient_top_overlay.xml"}, {"merged": "com.videoplayerapp-debug-38:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.videoplayerapp-main-40:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\drawable_circle_background_translucent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\drawable\\circle_background_translucent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-debug-38:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.videoplayerapp-main-40:\\anim\\fade_in.xml"}]