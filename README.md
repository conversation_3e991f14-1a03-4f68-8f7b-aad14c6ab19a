# تطبيق مشغل الفيديو - Video Player App

تطبيق Android لتشغيل الفيديوهات مكتوب بلغة Kotlin مع واجهة مستخدم جذابة وانيميشن متقدم.

## الميزات

### 🎥 تشغيل الفيديوهات
- تشغيل جميع أنواع الفيديوهات المدعومة
- مشغل ExoPlayer متقدم مع عناصر تحكم مخصصة
- دعم الوضع الأفقي للتشغيل بملء الشاشة
- عناصر تحكم سهلة الاستخدام (تشغيل، إيقاف، تقديم، ترجيع)

### 🎨 واجهة مستخدم جذابة
- تصميم Material Design 3
- ألوان داكنة عصرية
- انيميشن Lottie للتحميل
- انيميشن مخصص للعناصر
- تأثيرات بصرية متقدمة

### 📱 تجربة مستخدم متميزة
- قائمة فيديوهات مع صور مصغرة
- معلومات تفصيلية لكل فيديو (المدة، الحجم، الدقة)
- انيميشن دخول للعناصر
- تأثيرات النقر والتمرير

### 🔐 إدارة الصلاحيات
- طلب صلاحيات الوصول للملفات
- دعم Android 13+ (READ_MEDIA_VIDEO)
- واجهة سهلة لمنح الصلاحيات

## التقنيات المستخدمة

### 🛠️ التطوير
- **Kotlin** - لغة البرمجة الأساسية
- **Android SDK** - منصة التطوير
- **Material Design 3** - نظام التصميم

### 📚 المكتبات
- **ExoPlayer** - مشغل الفيديو المتقدم
- **Lottie** - انيميشن متقدم
- **Glide** - تحميل وعرض الصور
- **Dexter** - إدارة الصلاحيات
- **RecyclerView** - عرض القوائم
- **CardView** - عرض البطاقات

## هيكل المشروع

```
app/src/main/
├── java/com/videoplayerapp/
│   ├── MainActivity.kt          # الشاشة الرئيسية
│   ├── VideoPlayerActivity.kt   # شاشة تشغيل الفيديو
│   ├── VideoAdapter.kt          # محول قائمة الفيديوهات
│   ├── Video.kt                 # نموذج بيانات الفيديو
│   └── VideoUtils.kt            # أدوات مساعدة
├── res/
│   ├── layout/                  # ملفات التخطيط
│   ├── drawable/                # الأيقونات والرسوم
│   ├── values/                  # الألوان والنصوص والأنماط
│   ├── anim/                    # ملفات الانيميشن
│   └── raw/                     # ملف انيميشن Lottie
└── AndroidManifest.xml          # إعدادات التطبيق
```

## كيفية التشغيل

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK 24 أو أحدث
- Java 8 أو أحدث

### خطوات التشغيل
1. افتح المشروع في Android Studio
2. انتظر حتى يتم تحميل المكتبات
3. قم بتوصيل جهاز Android أو تشغيل محاكي
4. اضغط على "Run" أو استخدم Ctrl+R

### بناء APK
```bash
./gradlew assembleDebug
```

## الصلاحيات المطلوبة

- `READ_EXTERNAL_STORAGE` - للوصول للفيديوهات (Android 12 وأقل)
- `READ_MEDIA_VIDEO` - للوصول للفيديوهات (Android 13+)
- `INTERNET` - لتحميل المكتبات
- `ACCESS_NETWORK_STATE` - للتحقق من الاتصال

## الميزات المستقبلية

- [ ] إضافة قوائم تشغيل
- [ ] دعم الترجمة
- [ ] مشاركة الفيديوهات
- [ ] إعدادات التشغيل المتقدمة
- [ ] دعم البث المباشر
- [ ] تحسين الأداء

## المطور

تم تطوير هذا التطبيق باستخدام أفضل الممارسات في تطوير Android مع التركيز على:
- الأداء العالي
- تجربة المستخدم المتميزة
- التصميم العصري
- الكود النظيف والقابل للصيانة

---

**ملاحظة**: تأكد من وجود فيديوهات على الجهاز لاختبار التطبيق بشكل كامل.
