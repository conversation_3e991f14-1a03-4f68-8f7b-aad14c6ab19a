#Tue Jun 10 20:35:47 AST 2025
com.videoplayerapp-main-40\:/anim/fade_in.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_in.xml.flat
com.videoplayerapp-main-40\:/anim/slide_in_right.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right.xml.flat
com.videoplayerapp-main-40\:/anim/slide_up.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_up.xml.flat
com.videoplayerapp-main-40\:/drawable/circle_background.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.videoplayerapp-main-40\:/drawable/circle_background_primary.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_primary.xml.flat
com.videoplayerapp-main-40\:/drawable/circle_background_translucent.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_translucent.xml.flat
com.videoplayerapp-main-40\:/drawable/gradient_bottom_overlay.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_bottom_overlay.xml.flat
com.videoplayerapp-main-40\:/drawable/gradient_top_overlay.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_top_overlay.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_arrow_back.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_fast_forward.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_forward.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_fast_rewind.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fast_rewind.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_fullscreen.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fullscreen.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_launcher_background.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_launcher_foreground.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_more_vert.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_vert.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_pause.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_permission.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_permission.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_play_arrow.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_arrow.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_skip_next.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_next.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_skip_previous.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skip_previous.xml.flat
com.videoplayerapp-main-40\:/drawable/ic_video_library.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_library.xml.flat
com.videoplayerapp-main-40\:/mipmap-hdpi/ic_launcher.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.xml.flat
com.videoplayerapp-main-40\:/mipmap-hdpi/ic_launcher_round.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.xml.flat
com.videoplayerapp-main-40\:/mipmap-mdpi/ic_launcher.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.xml.flat
com.videoplayerapp-main-40\:/mipmap-mdpi/ic_launcher_round.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.xml.flat
com.videoplayerapp-main-40\:/mipmap-xhdpi/ic_launcher.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.xml.flat
com.videoplayerapp-main-40\:/mipmap-xhdpi/ic_launcher_round.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.xml.flat
com.videoplayerapp-main-40\:/mipmap-xxhdpi/ic_launcher.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.xml.flat
com.videoplayerapp-main-40\:/mipmap-xxhdpi/ic_launcher_round.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.xml.flat
com.videoplayerapp-main-40\:/mipmap-xxxhdpi/ic_launcher.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.xml.flat
com.videoplayerapp-main-40\:/mipmap-xxxhdpi/ic_launcher_round.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.xml.flat
com.videoplayerapp-main-40\:/raw/loading_animation.json=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\raw_loading_animation.json.flat
com.videoplayerapp-main-40\:/xml/backup_rules.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.videoplayerapp-main-40\:/xml/data_extraction_rules.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.videoplayerapp-mergeDebugResources-37\:/layout/activity_main.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.videoplayerapp-mergeDebugResources-37\:/layout/activity_video_player.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_video_player.xml.flat
com.videoplayerapp-mergeDebugResources-37\:/layout/custom_player_controls.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_custom_player_controls.xml.flat
com.videoplayerapp-mergeDebugResources-37\:/layout/item_video.xml=D\:\\ccc\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_video.xml.flat
