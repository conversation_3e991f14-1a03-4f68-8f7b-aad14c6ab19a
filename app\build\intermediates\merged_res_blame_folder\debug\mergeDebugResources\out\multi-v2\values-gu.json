{"logs": [{"outputFile": "com.videoplayerapp-mergeDebugResources-36:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5d9e96f88c57d15875888b62481fb57\\transformed\\exoplayer-ui-2.19.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,677,759,839,922,1021,1123,1200,1262,1351,1439,1503,1567,1627,1694,1807,1921,2032,2105,2183,2252,2328,2410,2490,2553,2616,2669,2727,2775,2836,2898,2960,3025,3087,3154,3217,3283,3350,3417,3470,3532,3608,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "281,484,672,754,834,917,1016,1118,1195,1257,1346,1434,1498,1562,1622,1689,1802,1916,2027,2100,2178,2247,2323,2405,2485,2548,2611,2664,2722,2770,2831,2893,2955,3020,3082,3149,3212,3278,3345,3412,3465,3527,3603,3679,3733"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,584,5139,5221,5301,5384,5483,5585,5662,5724,5813,5901,5965,6029,6089,6156,6269,6383,6494,6567,6645,6714,6790,6872,6952,7015,7774,7827,7885,7933,7994,8056,8118,8183,8245,8312,8375,8441,8508,8575,8628,8690,8766,8842", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "376,579,767,5216,5296,5379,5478,5580,5657,5719,5808,5896,5960,6024,6084,6151,6264,6378,6489,6562,6640,6709,6785,6867,6947,7010,7073,7822,7880,7928,7989,8051,8113,8178,8240,8307,8370,8436,8503,8570,8623,8685,8761,8837,8891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b85d503f681dad467ed38263c2abf31\\transformed\\core-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4055,4149,4252,4349,4451,4553,4651,13639", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "4144,4247,4344,4446,4548,4646,4768,13735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f77e990dfb2eeb1d5f51a8568b08c12f\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "940,1047,1151,1258,1345,1445,1565,1643,1720,1811,1904,1999,2093,2193,2286,2381,2475,2566,2657,2737,2843,2944,3041,3150,3250,3360,3520,13558", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "1042,1146,1253,1340,1440,1560,1638,1715,1806,1899,1994,2088,2188,2281,2376,2470,2561,2652,2732,2838,2939,3036,3145,3245,3355,3515,3618,13634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4f83af3dd64ab85dfcef8f00ae0b8bf9\\transformed\\exoplayer-core-2.19.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,266,334,409,477,576,672", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "120,185,261,329,404,472,571,667,746"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7078,7148,7213,7289,7357,7432,7500,7599,7695", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "7143,7208,7284,7352,7427,7495,7594,7690,7769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95c2f2dde71652be877f11a0d58ef6e2\\transformed\\material-1.10.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2825,2911,2992,3046,3098,3164,3237,3317,3402,3482,3553,3629,3708,3777,3884,3980,4058,4153,4249,4323,4398,4497,4548,4630,4697,4784,4874,4936,5000,5063,5130,5232,5337,5434,5536,5594,5650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2820,2906,2987,3041,3093,3159,3232,3312,3397,3477,3548,3624,3703,3772,3879,3975,4053,4148,4244,4318,4393,4492,4543,4625,4692,4779,4869,4931,4995,5058,5125,5227,5332,5429,5531,5589,5645,5723"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3623,3697,3769,3851,3957,4773,4872,4992,5076,8896,8987,9054,9113,9203,9266,9331,9395,9464,9526,9580,9695,9753,9814,9868,9941,10068,10154,10238,10371,10446,10522,10655,10741,10822,10876,10928,10994,11067,11147,11232,11312,11383,11459,11538,11607,11714,11810,11888,11983,12079,12153,12228,12327,12378,12460,12527,12614,12704,12766,12830,12893,12960,13062,13167,13264,13366,13424,13480", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "935,3692,3764,3846,3952,4050,4867,4987,5071,5134,8982,9049,9108,9198,9261,9326,9390,9459,9521,9575,9690,9748,9809,9863,9936,10063,10149,10233,10366,10441,10517,10650,10736,10817,10871,10923,10989,11062,11142,11227,11307,11378,11454,11533,11602,11709,11805,11883,11978,12074,12148,12223,12322,12373,12455,12522,12609,12699,12761,12825,12888,12955,13057,13162,13259,13361,13419,13475,13553"}}]}]}