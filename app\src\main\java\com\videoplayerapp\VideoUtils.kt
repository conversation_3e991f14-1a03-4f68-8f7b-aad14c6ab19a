package com.videoplayerapp

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

object VideoUtils {
    
    private const val TAG = "VideoUtils"
    
    suspend fun getAllVideos(context: Context): List<Video> = withContext(Dispatchers.IO) {
        val videos = mutableListOf<Video>()
        val contentResolver: ContentResolver = context.contentResolver
        
        val projection = arrayOf(
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.TITLE,
            MediaStore.Video.Media.DATA,
            MediaStore.Video.Media.DURATION,
            MediaStore.Video.Media.SIZE,
            MediaStore.Video.Media.RESOLUTION,
            MediaStore.Video.Media.DATE_ADDED
        )
        
        val sortOrder = "${MediaStore.Video.Media.DATE_ADDED} DESC"
        
        val cursor: Cursor? = contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            sortOrder
        )
        
        cursor?.use {
            val idColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
            val titleColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.TITLE)
            val dataColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
            val durationColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)
            val sizeColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE)
            val resolutionColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.RESOLUTION)
            val dateAddedColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED)
            
            while (it.moveToNext()) {
                try {
                    val id = it.getLong(idColumn)
                    val title = it.getString(titleColumn) ?: "Unknown"
                    val path = it.getString(dataColumn) ?: continue
                    val duration = it.getLong(durationColumn)
                    val size = it.getLong(sizeColumn)
                    val resolution = it.getString(resolutionColumn) ?: "Unknown"
                    val dateAdded = it.getLong(dateAddedColumn)
                    
                    // Check if file exists
                    if (File(path).exists()) {
                        val video = Video(
                            id = id,
                            title = title,
                            path = path,
                            duration = duration,
                            size = size,
                            resolution = resolution,
                            dateAdded = dateAdded
                        )
                        videos.add(video)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing video: ${e.message}")
                }
            }
        }
        
        return@withContext videos
    }
    
    fun getVideoThumbnail(videoPath: String): Bitmap? {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(videoPath)
            val bitmap = retriever.getFrameAtTime(1000000) // Get frame at 1 second
            retriever.release()
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "Error getting video thumbnail: ${e.message}")
            null
        }
    }
    
    fun getVideoUri(context: Context, videoId: Long): Uri {
        return Uri.withAppendedPath(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            videoId.toString()
        )
    }
    
    fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            else -> String.format("%.1f KB", kb)
        }
    }
    
    fun formatDuration(milliseconds: Long): String {
        val hours = milliseconds / 3600000
        val minutes = (milliseconds % 3600000) / 60000
        val seconds = (milliseconds % 60000) / 1000
        
        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }
}
