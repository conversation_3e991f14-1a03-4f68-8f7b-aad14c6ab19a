package com.videoplayerapp

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions

class VideoAdapter(
    private val context: Context,
    private val onVideoClick: (Video) -> Unit
) : ListAdapter<Video, VideoAdapter.VideoViewHolder>(VideoDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_video, parent, false)
        return VideoViewHolder(view)
    }

    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        val video = getItem(position)
        holder.bind(video)
        
        // Add entrance animation
        animateItemEntry(holder.itemView, position)
    }

    private fun animateItemEntry(view: View, position: Int) {
        view.alpha = 0f
        view.translationY = 100f
        
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        val slideUp = ObjectAnimator.ofFloat(view, "translationY", 100f, 0f)
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(fadeIn, slideUp)
        animatorSet.duration = 500
        animatorSet.startDelay = (position * 100).toLong()
        animatorSet.interpolator = AccelerateDecelerateInterpolator()
        animatorSet.start()
    }

    inner class VideoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: CardView = itemView.findViewById(R.id.cardView)
        private val videoThumbnail: ImageView = itemView.findViewById(R.id.videoThumbnail)
        private val playButton: ImageView = itemView.findViewById(R.id.playButton)
        private val videoTitle: TextView = itemView.findViewById(R.id.videoTitle)
        private val videoDuration: TextView = itemView.findViewById(R.id.videoDuration)
        private val videoSize: TextView = itemView.findViewById(R.id.videoSize)
        private val videoResolution: TextView = itemView.findViewById(R.id.videoResolution)

        fun bind(video: Video) {
            videoTitle.text = video.getFileName()
            videoDuration.text = video.getFormattedDuration()
            videoSize.text = video.getFormattedSize()
            videoResolution.text = video.resolution

            // Load video thumbnail using Glide
            val requestOptions = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .placeholder(R.drawable.ic_video_library)
                .error(R.drawable.ic_video_library)

            Glide.with(context)
                .load(video.path)
                .apply(requestOptions)
                .into(videoThumbnail)

            // Set click listeners with animation
            cardView.setOnClickListener {
                animateClick(cardView) {
                    onVideoClick(video)
                }
            }

            playButton.setOnClickListener {
                animateClick(playButton) {
                    onVideoClick(video)
                }
            }
        }

        private fun animateClick(view: View, action: () -> Unit) {
            val scaleDown = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.95f)
            val scaleDownY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.95f)
            val scaleUp = ObjectAnimator.ofFloat(view, "scaleX", 0.95f, 1f)
            val scaleUpY = ObjectAnimator.ofFloat(view, "scaleY", 0.95f, 1f)

            val scaleDownSet = AnimatorSet()
            scaleDownSet.playTogether(scaleDown, scaleDownY)
            scaleDownSet.duration = 100

            val scaleUpSet = AnimatorSet()
            scaleUpSet.playTogether(scaleUp, scaleUpY)
            scaleUpSet.duration = 100

            scaleDownSet.addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    scaleUpSet.start()
                    action()
                }
            })

            scaleDownSet.start()
        }
    }

    class VideoDiffCallback : DiffUtil.ItemCallback<Video>() {
        override fun areItemsTheSame(oldItem: Video, newItem: Video): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Video, newItem: Video): Boolean {
            return oldItem == newItem
        }
    }
}
