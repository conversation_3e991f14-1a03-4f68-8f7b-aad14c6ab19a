{"logs": [{"outputFile": "com.videoplayerapp-mergeDebugResources-36:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4f83af3dd64ab85dfcef8f00ae0b8bf9\\transformed\\exoplayer-core-2.19.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6194,6264,6334,6406,6472,6549,6616,6717,6810", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6259,6329,6401,6467,6544,6611,6712,6805,6875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5d9e96f88c57d15875888b62481fb57\\transformed\\exoplayer-ui-2.19.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3410,3476,3529,3593,3671,3749", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3405,3471,3524,3588,3666,3744,3803"}, "to": {"startLines": "2,11,15,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4173,4254,4337,4410,4509,4605,4679,4745,4841,4936,5002,5071,5138,5209,5327,5444,5565,5632,5718,5794,5868,5966,6066,6130,6880,6933,6991,7039,7100,7165,7227,7293,7363,7427,7488,7554,7619,7685,7738,7802,7880,7958", "endLines": "10,14,18,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "330,516,695,4249,4332,4405,4504,4600,4674,4740,4836,4931,4997,5066,5133,5204,5322,5439,5560,5627,5713,5789,5863,5961,6061,6125,6189,6928,6986,7034,7095,7160,7222,7288,7358,7422,7483,7549,7614,7680,7733,7797,7875,7953,8012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7b85d503f681dad467ed38263c2abf31\\transformed\\core-1.12.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3535,3637,3736,3836,3943,4053,8103", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3530,3632,3731,3831,3938,4048,4168,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f77e990dfb2eeb1d5f51a8568b08c12f\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,8017", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,8098"}}]}]}