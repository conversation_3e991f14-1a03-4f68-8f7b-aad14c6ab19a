<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    style="@style/CardViewStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Video Thumbnail -->
        <ImageView
            android:id="@+id/videoThumbnail"
            android:layout_width="120dp"
            android:layout_height="80dp"
            android:layout_marginEnd="12dp"
            android:background="@color/surface_color"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_video_library" />

        <!-- Play Button Overlay -->
        <ImageView
            android:id="@+id/playButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_background"
            android:padding="8dp"
            android:src="@drawable/ic_play_arrow"
            android:tint="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/videoThumbnail"
            app:layout_constraintEnd_toEndOf="@id/videoThumbnail"
            app:layout_constraintStart_toStartOf="@id/videoThumbnail"
            app:layout_constraintTop_toTopOf="@id/videoThumbnail" />

        <!-- Video Title -->
        <TextView
            android:id="@+id/videoTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/videoThumbnail"
            app:layout_constraintTop_toTopOf="@id/videoThumbnail"
            tools:text="Sample Video Title" />

        <!-- Video Duration -->
        <TextView
            android:id="@+id/videoDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/videoTitle"
            app:layout_constraintTop_toBottomOf="@id/videoTitle"
            tools:text="05:30" />

        <!-- Video Size -->
        <TextView
            android:id="@+id/videoSize"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/videoTitle"
            app:layout_constraintTop_toBottomOf="@id/videoDuration"
            tools:text="25.4 MB" />

        <!-- Video Resolution -->
        <TextView
            android:id="@+id/videoResolution"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/accent_color"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/videoThumbnail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/videoTitle"
            app:layout_constraintTop_toBottomOf="@id/videoSize"
            tools:text="1920x1080" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
