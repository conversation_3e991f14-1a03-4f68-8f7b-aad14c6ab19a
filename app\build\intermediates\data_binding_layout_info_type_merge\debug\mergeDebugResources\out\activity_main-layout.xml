<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.videoplayerapp" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="134" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="47"/></Target><Target id="@+id/loadingAnimation" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="36" startOffset="12" endLine="45" endOffset="60"/></Target><Target id="@+id/noVideosLayout" view="LinearLayout"><Expressions/><location startLine="48" startOffset="12" endLine="72" endOffset="26"/></Target><Target id="@+id/videosRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="75" startOffset="12" endLine="83" endOffset="53"/></Target><Target id="@+id/permissionLayout" view="LinearLayout"><Expressions/><location startLine="90" startOffset="4" endLine="132" endOffset="18"/></Target><Target id="@+id/grantPermissionButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="125" startOffset="8" endLine="130" endOffset="37"/></Target></Targets></Layout>