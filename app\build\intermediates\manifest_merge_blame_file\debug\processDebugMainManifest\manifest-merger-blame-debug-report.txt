1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoplayerapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for Android 12 and below -->
12    <uses-permission
12-->D:\ccc\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->D:\ccc\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->D:\ccc\app\src\main\AndroidManifest.xml:7:9-35
15
16    <!-- Permissions for Android 13+ -->
17    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
17-->D:\ccc\app\src\main\AndroidManifest.xml:10:5-75
17-->D:\ccc\app\src\main\AndroidManifest.xml:10:22-72
18
19    <!-- Network permissions -->
20    <uses-permission android:name="android.permission.INTERNET" />
20-->D:\ccc\app\src\main\AndroidManifest.xml:13:5-67
20-->D:\ccc\app\src\main\AndroidManifest.xml:13:22-64
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->D:\ccc\app\src\main\AndroidManifest.xml:14:5-79
21-->D:\ccc\app\src\main\AndroidManifest.xml:14:22-76
22
23    <!-- Photo picker permission for Android 14+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
24-->D:\ccc\app\src\main\AndroidManifest.xml:17:5-90
24-->D:\ccc\app\src\main\AndroidManifest.xml:17:22-87
25
26    <permission
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.videoplayerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.videoplayerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->D:\ccc\app\src\main\AndroidManifest.xml:19:5-47:19
33        android:allowBackup="true"
33-->D:\ccc\app\src\main\AndroidManifest.xml:20:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b85d503f681dad467ed38263c2abf31\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->D:\ccc\app\src\main\AndroidManifest.xml:21:9-65
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:fullBackupContent="@xml/backup_rules"
38-->D:\ccc\app\src\main\AndroidManifest.xml:22:9-54
39        android:icon="@mipmap/ic_launcher"
39-->D:\ccc\app\src\main\AndroidManifest.xml:23:9-43
40        android:label="@string/app_name"
40-->D:\ccc\app\src\main\AndroidManifest.xml:24:9-41
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->D:\ccc\app\src\main\AndroidManifest.xml:25:9-54
42        android:supportsRtl="true"
42-->D:\ccc\app\src\main\AndroidManifest.xml:26:9-35
43        android:theme="@style/Theme.VideoPlayerApp" >
43-->D:\ccc\app\src\main\AndroidManifest.xml:27:9-52
44        <activity
44-->D:\ccc\app\src\main\AndroidManifest.xml:30:9-38:20
45            android:name="com.videoplayerapp.MainActivity"
45-->D:\ccc\app\src\main\AndroidManifest.xml:31:13-41
46            android:exported="true"
46-->D:\ccc\app\src\main\AndroidManifest.xml:32:13-36
47            android:theme="@style/Theme.VideoPlayerApp.NoActionBar" >
47-->D:\ccc\app\src\main\AndroidManifest.xml:33:13-68
48            <intent-filter>
48-->D:\ccc\app\src\main\AndroidManifest.xml:34:13-37:29
49                <action android:name="android.intent.action.MAIN" />
49-->D:\ccc\app\src\main\AndroidManifest.xml:35:17-69
49-->D:\ccc\app\src\main\AndroidManifest.xml:35:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->D:\ccc\app\src\main\AndroidManifest.xml:36:17-77
51-->D:\ccc\app\src\main\AndroidManifest.xml:36:27-74
52            </intent-filter>
53        </activity>
54        <activity
54-->D:\ccc\app\src\main\AndroidManifest.xml:40:9-45:70
55            android:name="com.videoplayerapp.VideoPlayerActivity"
55-->D:\ccc\app\src\main\AndroidManifest.xml:41:13-48
56            android:configChanges="orientation|screenSize|keyboardHidden"
56-->D:\ccc\app\src\main\AndroidManifest.xml:43:13-74
57            android:exported="false"
57-->D:\ccc\app\src\main\AndroidManifest.xml:42:13-37
58            android:screenOrientation="landscape"
58-->D:\ccc\app\src\main\AndroidManifest.xml:44:13-50
59            android:theme="@style/Theme.VideoPlayerApp.FullScreen" />
59-->D:\ccc\app\src\main\AndroidManifest.xml:45:13-67
60        <activity
60-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a71c94c0909e913c38c158e69ed26cb4\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
61            android:name="com.karumi.dexter.DexterActivity"
61-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a71c94c0909e913c38c158e69ed26cb4\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
62            android:theme="@style/Dexter.Internal.Theme.Transparent" />
62-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a71c94c0909e913c38c158e69ed26cb4\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
63
64        <provider
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
65            android:name="androidx.startup.InitializationProvider"
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
66            android:authorities="com.videoplayerapp.androidx-startup"
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
67            android:exported="false" >
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
68            <meta-data
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.emoji2.text.EmojiCompatInitializer"
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
70                android:value="androidx.startup" />
70-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715afe63bc76ba69c15490df988f5165\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4305c108b550adf4093514cf3e701e3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
72-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4305c108b550adf4093514cf3e701e3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
73                android:value="androidx.startup" />
73-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4305c108b550adf4093514cf3e701e3\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
76                android:value="androidx.startup" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
77        </provider>
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb669cddc17ee89ecbc4b5734f228092\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98    </application>
99
100</manifest>
