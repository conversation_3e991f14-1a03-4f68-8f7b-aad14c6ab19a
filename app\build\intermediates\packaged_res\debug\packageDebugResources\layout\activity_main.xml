<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="@string/app_name"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Loading Animation -->
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/loadingAnimation"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:visibility="visible"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/loading_animation" />

            <!-- No Videos Message -->
            <LinearLayout
                android:id="@+id/noVideosLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_video_library"
                    android:tint="@color/text_secondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_videos_found"
                    android:textColor="@color/text_secondary"
                    android:textSize="18sp" />

            </LinearLayout>

            <!-- Videos RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/videosRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                android:visibility="gone"
                tools:listitem="@layout/item_video" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Permission Request Layout -->
    <LinearLayout
        android:id="@+id/permissionLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_color"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_permission"
            android:tint="@color/primary_color" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/permission_required"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:gravity="center"
            android:text="@string/permission_message"
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/grantPermissionButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/grant_permission"
            app:cornerRadius="25dp" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
