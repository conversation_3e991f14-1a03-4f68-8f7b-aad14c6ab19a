<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_video" modulePackage="com.videoplayerapp" filePath="app\src\main\res\layout\item_video.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_video_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="103" endOffset="35"/></Target><Target id="@+id/videoThumbnail" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="29" endOffset="52"/></Target><Target id="@+id/playButton" view="ImageView"><Expressions/><location startLine="32" startOffset="8" endLine="43" endOffset="67"/></Target><Target id="@+id/videoTitle" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="59" endOffset="45"/></Target><Target id="@+id/videoDuration" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="72" endOffset="32"/></Target><Target id="@+id/videoSize" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="85" endOffset="34"/></Target><Target id="@+id/videoResolution" view="TextView"><Expressions/><location startLine="88" startOffset="8" endLine="99" endOffset="36"/></Target></Targets></Layout>